import React, { useState } from 'react';
import { useThemeStore } from '../../../../stores/themeStore';
import type { Message as MessageType, User, Reaction } from '../../types';

export interface MessageProps {
  message: MessageType;
  author: User;
  showAvatar?: boolean;
  showTimestamp?: boolean;
  isCompact?: boolean;
  onReaction?: (messageId: string, emoji: string) => void;
  onReply?: (messageId: string) => void;
  onEdit?: (messageId: string) => void;
  onDelete?: (messageId: string) => void;
  onPin?: (messageId: string) => void;
  className?: string;
  'data-testid'?: string;
}

export const Message: React.FC<MessageProps> = ({
  message,
  author,
  showAvatar = true,
  showTimestamp = true,
  isCompact = false,
  onReaction,
  onReply,
  onEdit,
  onDelete,
  onPin,
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();
  const [showActions, setShowActions] = useState(false);
  const [showReactionPicker, setShowReactionPicker] = useState(false);

  const formatTimestamp = (timestamp: Date) => {
    const now = new Date();
    const messageDate = new Date(timestamp);
    const diffInHours = (now.getTime() - messageDate.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return messageDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else if (diffInHours < 24 * 7) {
      return messageDate.toLocaleDateString([], { weekday: 'short', hour: '2-digit', minute: '2-digit' });
    } else {
      return messageDate.toLocaleDateString([], { month: 'short', day: 'numeric', hour: '2-digit', minute: '2-digit' });
    }
  };

  const handleReactionClick = (emoji: string) => {
    onReaction?.(message.id, emoji);
    setShowReactionPicker(false);
  };

  const renderReactions = () => {
    if (message.reactions.length === 0) return null;

    return (
      <div className="flex flex-wrap gap-1 mt-2">
        {message.reactions.map((reaction) => (
          <button
            key={reaction.emoji}
            onClick={() => handleReactionClick(reaction.emoji)}
            className="flex items-center space-x-1 px-2 py-1 rounded-full text-xs hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
            style={{
              backgroundColor: colors.backgroundSecondary,
              color: colors.text,
            }}
          >
            <span>{reaction.emoji}</span>
            <span>{reaction.count}</span>
          </button>
        ))}
      </div>
    );
  };

  const renderMessageActions = () => {
    if (!showActions) return null;

    return (
      <div className="absolute top-0 right-0 -mt-2 flex items-center space-x-1 bg-white dark:bg-gray-800 border rounded-lg shadow-lg p-1">
        <button
          onClick={() => setShowReactionPicker(!showReactionPicker)}
          className="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded"
          title="Add reaction"
        >
          😊
        </button>
        {onReply && (
          <button
            onClick={() => onReply(message.id)}
            className="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded"
            title="Reply"
          >
            💬
          </button>
        )}
        {onPin && (
          <button
            onClick={() => onPin(message.id)}
            className="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded"
            title="Pin message"
          >
            📌
          </button>
        )}
        {onEdit && (
          <button
            onClick={() => onEdit(message.id)}
            className="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded"
            title="Edit"
          >
            ✏️
          </button>
        )}
        {onDelete && (
          <button
            onClick={() => onDelete(message.id)}
            className="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded text-red-500"
            title="Delete"
          >
            🗑️
          </button>
        )}
      </div>
    );
  };

  const renderReactionPicker = () => {
    if (!showReactionPicker) return null;

    const commonEmojis = ['👍', '❤️', '😂', '😮', '😢', '😡', '🎉', '🚀'];

    return (
      <div className="absolute top-8 right-0 bg-white dark:bg-gray-800 border rounded-lg shadow-lg p-2 z-10">
        <div className="grid grid-cols-4 gap-1">
          {commonEmojis.map((emoji) => (
            <button
              key={emoji}
              onClick={() => handleReactionClick(emoji)}
              className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded text-lg"
            >
              {emoji}
            </button>
          ))}
        </div>
      </div>
    );
  };

  const renderMentions = (content: string) => {
    // Simple mention highlighting - in real app, this would be more sophisticated
    return content.replace(/@(\w+)/g, '<span class="text-blue-500 font-medium">@$1</span>');
  };

  if (message.isDeleted) {
    return (
      <div
        className={`flex items-center space-x-3 py-2 ${className}`}
        data-testid={testId}
      >
        <div className="text-sm italic" style={{ color: colors.textSecondary }}>
          This message was deleted
        </div>
      </div>
    );
  }

  return (
    <div
      className={`relative group ${isCompact ? 'py-1' : 'py-3'} ${className}`}
      onMouseEnter={() => setShowActions(true)}
      onMouseLeave={() => {
        setShowActions(false);
        setShowReactionPicker(false);
      }}
      data-testid={testId}
    >
      <div className="flex items-start space-x-3">
        {/* Avatar */}
        {showAvatar && !isCompact && (
          <div className="flex-shrink-0">
            <div
              className="w-10 h-10 rounded-full flex items-center justify-center text-white font-semibold"
              style={{ backgroundColor: colors.primary }}
            >
              {author.avatar || author.name.charAt(0).toUpperCase()}
            </div>
            <div
              className={`absolute w-3 h-3 rounded-full border-2 border-white -mt-1 ml-7 ${
                author.status === 'online'
                  ? 'bg-green-500'
                  : author.status === 'away'
                  ? 'bg-yellow-500'
                  : author.status === 'busy'
                  ? 'bg-red-500'
                  : 'bg-gray-400'
              }`}
            />
          </div>
        )}

        {/* Message Content */}
        <div className="flex-1 min-w-0">
          {/* Header */}
          {!isCompact && (
            <div className="flex items-center space-x-2 mb-1">
              <span className="font-semibold" style={{ color: colors.text }}>
                {author.name}
              </span>
              {showTimestamp && (
                <span className="text-xs" style={{ color: colors.textSecondary }}>
                  {formatTimestamp(message.timestamp)}
                </span>
              )}
              {message.editedAt && (
                <span className="text-xs" style={{ color: colors.textSecondary }}>
                  (edited)
                </span>
              )}
            </div>
          )}

          {/* Message Text */}
          <div
            className="text-sm leading-relaxed"
            style={{ color: colors.text }}
            dangerouslySetInnerHTML={{
              __html: renderMentions(message.content),
            }}
          />

          {/* Attachments */}
          {message.attachments.length > 0 && (
            <div className="mt-2 space-y-2">
              {message.attachments.map((attachment) => (
                <div
                  key={attachment.id}
                  className="flex items-center space-x-2 p-2 border rounded-lg"
                  style={{
                    borderColor: colors.border,
                    backgroundColor: colors.backgroundSecondary,
                  }}
                >
                  <div className="text-lg">
                    {attachment.type === 'image' ? '🖼️' :
                     attachment.type === 'video' ? '🎥' :
                     attachment.type === 'audio' ? '🎵' :
                     attachment.type === 'document' ? '📄' : '📎'}
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium truncate" style={{ color: colors.text }}>
                      {attachment.name}
                    </p>
                    <p className="text-xs" style={{ color: colors.textSecondary }}>
                      {(attachment.size / 1024 / 1024).toFixed(2)} MB
                    </p>
                  </div>
                  <button
                    className="text-sm px-2 py-1 rounded hover:bg-gray-100 dark:hover:bg-gray-700"
                    style={{ color: colors.primary }}
                  >
                    Download
                  </button>
                </div>
              ))}
            </div>
          )}

          {/* Reactions */}
          {renderReactions()}

          {/* Thread indicator */}
          {message.parentMessageId && (
            <div className="mt-2">
              <button
                className="text-xs hover:underline"
                style={{ color: colors.primary }}
              >
                View thread
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Message Actions */}
      {renderMessageActions()}

      {/* Reaction Picker */}
      {renderReactionPicker()}
    </div>
  );
};
